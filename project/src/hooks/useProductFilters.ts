import { useState, useEffect, useCallback, useMemo } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Product, FilterState, FilterConfig } from '../types';
import {
  getDefaultFilterState,
  getFilterConfig,
  applyFilters,
  sortProducts,
  filtersToSearchParams,
  searchParamsToFilters,
  hasActiveFilters as checkActiveFilters
} from '../utils/filterUtils';
import { searchProducts } from '../utils/persianSearch';

interface UseProductFiltersOptions {
  products: Product[];
  searchQuery?: string;
  updateUrl?: boolean;
}

export const useProductFilters = ({
  products,
  searchQuery = '',
  updateUrl = true
}: UseProductFiltersOptions) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [hasProductsLoaded, setHasProductsLoaded] = useState(false);

  // Initialize filters from URL or defaults
  const [filters, setFilters] = useState<FilterState>(() => {
    const defaultFilters = getDefaultFilterState(products);
    if (updateUrl) {
      const urlParams = new URLSearchParams(location.search);
      const urlFilters = searchParamsToFilters(urlParams);
      return { ...defaultFilters, ...urlFilters };
    }
    return defaultFilters;
  });

  // Track if filters were initialized from URL
  const [filtersInitializedFromUrl, setFiltersInitializedFromUrl] = useState(false);

  // Get filter configuration
  const filterConfig = useMemo(() => getFilterConfig(products), [products]);

  // Handle products loading and filter initialization
  useEffect(() => {
    if (products.length > 0 && !hasProductsLoaded) {
      setHasProductsLoaded(true);

      // Re-initialize filters from URL with correct product data
      if (updateUrl && !filtersInitializedFromUrl) {
        const urlParams = new URLSearchParams(location.search);
        const urlFilters = searchParamsToFilters(urlParams);
        const defaultFilters = getDefaultFilterState(products);

        // Merge URL filters with defaults based on actual product data
        setFilters({ ...defaultFilters, ...urlFilters });
        setFiltersInitializedFromUrl(true);
      } else if (!filtersInitializedFromUrl) {
        // No URL params, just update price range to match actual products
        const newPriceRange = getFilterConfig(products).priceRange;
        setFilters(prev => ({
          ...prev,
          priceRange: [0, newPriceRange.max]
        }));
        setFiltersInitializedFromUrl(true);
      }
    }
  }, [products, hasProductsLoaded, filtersInitializedFromUrl, updateUrl, location.search]);

  // Apply search and filters to products
  const filteredProducts = useMemo(() => {
    let result = [...products];

    // Apply search first if query exists
    if (searchQuery && searchQuery.length >= 2) {
      result = searchProducts(result, searchQuery, {
        searchInDescription: true,
        searchInIngredients: true,
        searchInBenefits: true
      });
    }

    // Apply filters
    result = applyFilters(result, filters);

    // Apply sorting
    result = sortProducts(result, filters.sortBy);

    return result;
  }, [products, searchQuery, filters]);

  // Update URL when filters change (skip initial load and products loading)
  useEffect(() => {
    if (updateUrl && !isInitialLoad && hasProductsLoaded && filtersInitializedFromUrl) {
      const searchParams = filtersToSearchParams(filters, products);

      // Preserve existing search query
      if (searchQuery) {
        searchParams.set('search', searchQuery);
      }

      // Only navigate if there are meaningful changes
      const currentParams = new URLSearchParams(location.search);
      const newParamsString = searchParams.toString();
      const currentParamsString = currentParams.toString();

      // Don't update URL if no meaningful filters are applied and URL is already clean
      const hasFilters = checkActiveFilters(filters, products);
      const hasSearchQuery = Boolean(searchQuery);

      if (newParamsString !== currentParamsString) {
        // Only update if we have meaningful filters/search or need to clear the URL
        if (hasFilters || hasSearchQuery || currentParamsString) {
          navigate({
            pathname: location.pathname,
            search: newParamsString
          }, { replace: true });
        }
      }
    }
  }, [filters, searchQuery, updateUrl, navigate, location.pathname, products, isInitialLoad, hasProductsLoaded, filtersInitializedFromUrl]);

  // Mark initial load as complete after first render
  useEffect(() => {
    if (isInitialLoad) {
      setIsInitialLoad(false);
    }
  }, [isInitialLoad]);

  // Filter update functions
  const updateCategories = useCallback((categories: string[]) => {
    setFilters(prev => ({ ...prev, categories }));
  }, []);

  const toggleCategory = useCallback((category: string) => {
    setFilters(prev => ({
      ...prev,
      categories: prev.categories.includes(category)
        ? prev.categories.filter(c => c !== category)
        : [...prev.categories, category]
    }));
  }, []);

  const updateBrands = useCallback((brands: string[]) => {
    setFilters(prev => ({ ...prev, brands }));
  }, []);

  const toggleBrand = useCallback((brand: string) => {
    setFilters(prev => ({
      ...prev,
      brands: prev.brands.includes(brand)
        ? prev.brands.filter(b => b !== brand)
        : [...prev.brands, brand]
    }));
  }, []);

  const updatePriceRange = useCallback((priceRange: [number, number]) => {
    setFilters(prev => ({ ...prev, priceRange }));
  }, []);

  const updateStockStatus = useCallback((stockStatus: FilterState['stockStatus']) => {
    setFilters(prev => ({ ...prev, stockStatus }));
  }, []);

  const updateProductTypes = useCallback((productTypes: Partial<FilterState['productTypes']>) => {
    setFilters(prev => ({
      ...prev,
      productTypes: { ...prev.productTypes, ...productTypes }
    }));
  }, []);

  const toggleProductType = useCallback((type: keyof FilterState['productTypes']) => {
    setFilters(prev => ({
      ...prev,
      productTypes: {
        ...prev.productTypes,
        [type]: !prev.productTypes[type]
      }
    }));
  }, []);

  const updateRating = useCallback((rating: number) => {
    setFilters(prev => ({ ...prev, rating }));
  }, []);

  const updateSortBy = useCallback((sortBy: string) => {
    setFilters(prev => ({ ...prev, sortBy }));
  }, []);

  const clearFilters = useCallback(() => {
    setFilters(getDefaultFilterState(products));
  }, [products]);

  const clearFilter = useCallback((filterType: keyof FilterState) => {
    const defaultFilters = getDefaultFilterState(products);
    setFilters(prev => ({
      ...prev,
      [filterType]: defaultFilters[filterType]
    }));
  }, [products]);

  // Get active filter count
  const activeFilterCount = useMemo(() => {
    const defaultFilters = getDefaultFilterState(products);
    let count = 0;

    if (filters.categories.length > 0) count++;
    if (filters.brands.length > 0) count++;
    if (filters.priceRange[0] !== defaultFilters.priceRange[0] ||
        filters.priceRange[1] !== defaultFilters.priceRange[1]) count++;
    if (filters.stockStatus !== defaultFilters.stockStatus) count++;
    if (filters.productTypes.isNew || filters.productTypes.isBestSeller || filters.productTypes.hasDiscount) count++;
    if (filters.rating > 0) count++;

    return count;
  }, [filters, products]);

  // Check if filters are applied
  const hasActiveFilters = useMemo(() => activeFilterCount > 0, [activeFilterCount]);

  // Get filter summary for display
  const filterSummary = useMemo(() => {
    const summary: string[] = [];

    if (filters.categories.length > 0) {
      const categoryNames = filters.categories.map(slug => 
        filterConfig.categories.find(cat => cat.value === slug)?.label
      ).filter(Boolean);
      summary.push(`دسته‌بندی: ${categoryNames.join('، ')}`);
    }

    if (filters.brands.length > 0) {
      summary.push(`برند: ${filters.brands.join('، ')}`);
    }

    const defaultPriceRange = filterConfig.priceRange;
    if (filters.priceRange[0] !== 0 || filters.priceRange[1] !== defaultPriceRange.max) {
      summary.push(`قیمت: ${filters.priceRange[0].toLocaleString()} - ${filters.priceRange[1].toLocaleString()} تومان`);
    }

    if (filters.stockStatus !== 'all') {
      const stockLabels = {
        inStock: 'موجود',
        outOfStock: 'ناموجود',
        lowStock: 'کم موجود'
      };
      summary.push(`موجودی: ${stockLabels[filters.stockStatus]}`);
    }

    const activeTypes = [];
    if (filters.productTypes.isNew) activeTypes.push('جدید');
    if (filters.productTypes.isBestSeller) activeTypes.push('پرفروش');
    if (filters.productTypes.hasDiscount) activeTypes.push('تخفیف‌دار');
    if (activeTypes.length > 0) {
      summary.push(`نوع: ${activeTypes.join('، ')}`);
    }

    if (filters.rating > 0) {
      summary.push(`امتیاز: ${filters.rating}+ ستاره`);
    }

    return summary;
  }, [filters, filterConfig]);

  return {
    // State
    filters,
    filteredProducts,
    filterConfig,
    
    // Computed
    activeFilterCount,
    hasActiveFilters,
    filterSummary,
    
    // Actions
    updateCategories,
    toggleCategory,
    updateBrands,
    toggleBrand,
    updatePriceRange,
    updateStockStatus,
    updateProductTypes,
    toggleProductType,
    updateRating,
    updateSortBy,
    clearFilters,
    clearFilter,
    
    // Direct filter setters
    setFilters
  };
};
