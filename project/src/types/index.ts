// Product Variant Types
export interface ProductVariant {
  id: string;
  name: string;
  value: string;
  type: 'color' | 'size' | 'volume' | 'weight';
  price?: number; // Additional price for this variant
  discountedPrice?: number;
  stock: number;
  imageSrc?: string; // Variant-specific image
  isDefault?: boolean;
  colorCode?: string; // For color variants
  disabled?: boolean;
}

export interface ProductVariantGroup {
  type: 'color' | 'size' | 'volume' | 'weight';
  name: string;
  variants: ProductVariant[];
  required: boolean;
}

export interface SelectedVariants {
  [variantType: string]: ProductVariant;
}

export interface Product {
  id: number;
  name: string;
  category: string;
  price: number;
  discountedPrice?: number;
  rating: number;
  reviewCount: number;
  imageSrc: string;
  images?: string[]; // Additional images for gallery
  description: string;
  benefits: string[];
  ingredients: string[];
  howToUse: string[];
  skinType?: string[];
  tags?: string[];
  isNew?: boolean;
  isBestSeller?: boolean;
  stock: number;
  inStock?: boolean;
  featured?: boolean;
  brand?: string;
  size?: string;
  weight?: string;
  variants?: ProductVariantGroup[]; // Product variants
  hasVariants?: boolean; // Quick check for variants
}

export interface Category {
  id: number;
  name: string;
  description: string;
  imageSrc: string;
  slug: string;
}

export interface Testimonial {
  id: number;
  name: string;
  avatar: string;
  rating: number;
  text: string;
  date: string;
  productId?: number;
}

export interface CartItem {
  product: Product;
  quantity: number;
  selectedVariants?: SelectedVariants;
  variantKey?: string; // Unique key for variant combination
}

export interface CartContextType {
  items: CartItem[];
  totalItems: number;
  totalPrice: number;
  addItem: (product: Product, quantity?: number, selectedVariants?: SelectedVariants) => void;
  removeItem: (productId: number, variantKey?: string) => void;
  updateQuantity: (productId: number, quantity: number, variantKey?: string) => void;
  clearCart: () => void;
  isOpen: boolean;
  toggleCart: () => void;
}

// Filter Types
export interface FilterState {
  categories: string[];
  brands: string[];
  priceRange: [number, number];
  stockStatus: 'all' | 'inStock' | 'outOfStock' | 'lowStock';
  productTypes: {
    isNew: boolean;
    isBestSeller: boolean;
    hasDiscount: boolean;
  };
  rating: number;
  sortBy: string;
}

export interface FilterOption {
  value: string;
  label: string;
  count?: number;
  disabled?: boolean;
}

export interface PriceRange {
  min: number;
  max: number;
  step: number;
}

export interface FilterConfig {
  categories: FilterOption[];
  brands: FilterOption[];
  priceRange: PriceRange;
  sortOptions: FilterOption[];
}