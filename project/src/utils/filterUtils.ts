import { Product, FilterState, FilterOption, FilterConfig } from '../types';
import { categories } from '../data/categories';

/**
 * Get all unique brands from products
 */
export const getAllBrands = (products: Product[] = []): FilterOption[] => {
  const brandCounts = new Map<string, number>();

  products.forEach(product => {
    if (product.brand) {
      brandCounts.set(product.brand, (brandCounts.get(product.brand) || 0) + 1);
    }
  });

  return Array.from(brandCounts.entries())
    .map(([brand, count]) => ({
      value: brand,
      label: brand,
      count
    }))
    .sort((a, b) => a.label.localeCompare(b.label, 'fa'));
};

/**
 * Get category filter options with product counts
 */
export const getCategoryOptions = (products: Product[] = []): FilterOption[] => {
  const categoryCounts = new Map<string, number>();

  products.forEach(product => {
    categoryCounts.set(product.category, (categoryCounts.get(product.category) || 0) + 1);
  });

  return categories.map(category => ({
    value: category.slug,
    label: category.name,
    count: categoryCounts.get(category.name) || 0
  }));
};

/**
 * Get price range from all products
 */
export const getPriceRange = (products: Product[] = []) => {
  if (products.length === 0) {
    return { min: 0, max: 10000000, step: 10000 }; // Default range
  }

  const prices = products.map(product => product.discountedPrice || product.price);
  return {
    min: Math.min(...prices),
    max: Math.max(...prices),
    step: 10000
  };
};

/**
 * Get sort options
 */
export const getSortOptions = (): FilterOption[] => [
  { value: 'newest', label: 'جدیدترین' },
  { value: 'oldest', label: 'قدیمی‌ترین' },
  { value: 'price-low', label: 'قیمت: کم به زیاد' },
  { value: 'price-high', label: 'قیمت: زیاد به کم' },
  { value: 'rating', label: 'بالاترین امتیاز' },
  { value: 'popular', label: 'محبوب‌ترین' },
  { value: 'name-asc', label: 'نام: الف تا ی' },
  { value: 'name-desc', label: 'نام: ی تا الف' }
];

/**
 * Get complete filter configuration
 */
export const getFilterConfig = (products: Product[] = []): FilterConfig => ({
  categories: getCategoryOptions(products),
  brands: getAllBrands(products),
  priceRange: getPriceRange(products),
  sortOptions: getSortOptions()
});

/**
 * Apply filters to products
 */
export const applyFilters = (products: Product[], filters: FilterState): Product[] => {
  let filtered = [...products];
  
  // Filter by categories
  if (filters.categories.length > 0) {
    filtered = filtered.filter(product => {
      const categorySlug = categories.find(cat => cat.name === product.category)?.slug;
      return categorySlug && filters.categories.includes(categorySlug);
    });
  }
  
  // Filter by brands
  if (filters.brands.length > 0) {
    filtered = filtered.filter(product => 
      product.brand && filters.brands.includes(product.brand)
    );
  }
  
  // Filter by price range
  filtered = filtered.filter(product => {
    const price = product.discountedPrice || product.price;
    return price >= filters.priceRange[0] && price <= filters.priceRange[1];
  });
  
  // Filter by stock status
  switch (filters.stockStatus) {
    case 'inStock':
      filtered = filtered.filter(product => product.stock > 0);
      break;
    case 'outOfStock':
      filtered = filtered.filter(product => product.stock === 0);
      break;
    case 'lowStock':
      filtered = filtered.filter(product => product.stock > 0 && product.stock <= 5);
      break;
    // 'all' - no filtering
  }
  
  // Filter by product types
  if (filters.productTypes.isNew) {
    filtered = filtered.filter(product => product.isNew);
  }
  
  if (filters.productTypes.isBestSeller) {
    filtered = filtered.filter(product => product.isBestSeller);
  }
  
  if (filters.productTypes.hasDiscount) {
    filtered = filtered.filter(product => product.discountedPrice);
  }
  
  // Filter by rating
  if (filters.rating > 0) {
    filtered = filtered.filter(product => product.rating >= filters.rating);
  }
  
  return filtered;
};

/**
 * Sort products based on sort option
 */
export const sortProducts = (products: Product[], sortBy: string): Product[] => {
  const sorted = [...products];
  
  switch (sortBy) {
    case 'price-low':
      return sorted.sort((a, b) => {
        const aPrice = a.discountedPrice || a.price;
        const bPrice = b.discountedPrice || b.price;
        return aPrice - bPrice;
      });
      
    case 'price-high':
      return sorted.sort((a, b) => {
        const aPrice = a.discountedPrice || a.price;
        const bPrice = b.discountedPrice || b.price;
        return bPrice - aPrice;
      });
      
    case 'rating':
      return sorted.sort((a, b) => b.rating - a.rating);
      
    case 'popular':
      return sorted.sort((a, b) => b.reviewCount - a.reviewCount);
      
    case 'name-asc':
      return sorted.sort((a, b) => a.name.localeCompare(b.name, 'fa'));
      
    case 'name-desc':
      return sorted.sort((a, b) => b.name.localeCompare(a.name, 'fa'));
      
    case 'oldest':
      return sorted.sort((a, b) => a.id - b.id);
      
    case 'newest':
    default:
      return sorted.sort((a, b) => b.id - a.id);
  }
};

/**
 * Get default filter state
 */
export const getDefaultFilterState = (products: Product[] = []): FilterState => ({
  categories: [],
  brands: [],
  priceRange: [0, getPriceRange(products).max],
  stockStatus: 'all',
  productTypes: {
    isNew: false,
    isBestSeller: false,
    hasDiscount: false
  },
  rating: 0,
  sortBy: 'newest'
});

/**
 * Convert filter state to URL search params
 * Only adds parameters that differ from default values
 */
export const filtersToSearchParams = (filters: FilterState, products: Product[] = []): URLSearchParams => {
  const params = new URLSearchParams();
  const defaultFilters = getDefaultFilterState(products);

  if (filters.categories.length > 0) {
    params.set('categories', filters.categories.join(','));
  }

  if (filters.brands.length > 0) {
    params.set('brands', filters.brands.join(','));
  }

  // Only add price range params if they differ from the default range
  const defaultPriceRange = defaultFilters.priceRange;
  if (filters.priceRange[0] !== defaultPriceRange[0] || filters.priceRange[1] !== defaultPriceRange[1]) {
    params.set('priceMin', filters.priceRange[0].toString());
    params.set('priceMax', filters.priceRange[1].toString());
  }

  if (filters.stockStatus !== 'all') {
    params.set('stock', filters.stockStatus);
  }

  if (filters.productTypes.isNew) params.set('new', 'true');
  if (filters.productTypes.isBestSeller) params.set('bestseller', 'true');
  if (filters.productTypes.hasDiscount) params.set('discount', 'true');

  if (filters.rating > 0) {
    params.set('rating', filters.rating.toString());
  }

  if (filters.sortBy !== 'newest') {
    params.set('sort', filters.sortBy);
  }

  return params;
};

/**
 * Convert URL search params to filter state
 */
export const searchParamsToFilters = (searchParams: URLSearchParams): Partial<FilterState> => {
  const filters: Partial<FilterState> = {};
  
  const categories = searchParams.get('categories');
  if (categories) {
    filters.categories = categories.split(',');
  }
  
  const brands = searchParams.get('brands');
  if (brands) {
    filters.brands = brands.split(',');
  }
  
  const priceMin = searchParams.get('priceMin');
  const priceMax = searchParams.get('priceMax');
  if (priceMin || priceMax) {
    filters.priceRange = [
      priceMin ? parseInt(priceMin) : 0,
      priceMax ? parseInt(priceMax) : 10000000
    ];
  }
  
  const stock = searchParams.get('stock');
  if (stock && ['inStock', 'outOfStock', 'lowStock'].includes(stock)) {
    filters.stockStatus = stock as FilterState['stockStatus'];
  }
  
  filters.productTypes = {
    isNew: searchParams.get('new') === 'true',
    isBestSeller: searchParams.get('bestseller') === 'true',
    hasDiscount: searchParams.get('discount') === 'true'
  };
  
  const rating = searchParams.get('rating');
  if (rating) {
    filters.rating = parseInt(rating);
  }
  
  const sort = searchParams.get('sort');
  if (sort) {
    filters.sortBy = sort;
  }
  
  return filters;
};
